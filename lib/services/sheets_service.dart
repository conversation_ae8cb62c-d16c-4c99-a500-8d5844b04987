import 'package:googleapis/sheets/v4.dart';
import 'package:googleapis_auth/auth_io.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;

class SheetsService {
  static const _spreadsheetId = '1cAE3Pe9TY_5B2BFnfL4kGJNTWcrknH7BKdDgjbU8EXw';
  static const _credentials = {
    'client_id':
        '283795183027-vqv88amfujomub6dh1hqapaufr362k18.apps.googleusercontent.com',
    'client_secret': '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',
    'redirect_uri': 'http://localhost:8080',
    'scopes': [SheetsApi.spreadsheetsScope]
  };

  late SheetsApi _sheetsApi;
  late ClientId _clientId;
  bool _isInitialized = false;

  Future<void> init() async {
    // Initialize timezone data
    tz_data.initializeTimeZones();
    if (_isInitialized) return;

    _clientId = ClientId(_credentials['client_id']! as String,
        _credentials['client_secret']! as String?);
    final client = await clientViaUserConsent(
      _clientId,
      [SheetsApi.spreadsheetsScope],
      (url) async {
        if (await canLaunchUrl(Uri.parse(url))) {
          await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
        } else {
          print('Could not launch consent page: $url');
        }
      },
    );

    _sheetsApi = SheetsApi(client);
    _isInitialized = true;
  }

  Future<void> addOrder(List<Map<String, dynamic>> items, String paymentMethod,
      double total) async {
    await init();

    // Convert current time to Mexico City timezone
    final mexicoCity = tz.getLocation('America/Mexico_City');
    final timestamp = tz.TZDateTime.now(mexicoCity).toString();
    final values = [
      [timestamp, paymentMethod, total.toString()],
      ...items.map((item) =>
          [item['name'], item['quantity'].toString(), item['price'].toString()])
    ];

    final valueRange = ValueRange(values: values);

    await _sheetsApi.spreadsheets.values.append(
      valueRange,
      _spreadsheetId,
      'Sheet1!A:C',
      valueInputOption: 'USER_ENTERED',
    );
  }
}
