import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:kiji_tablet/interfaces/product.dart';

class AirtableService {
  static const String _baseId = 'app1ojw3VVtPNbQwn';
  static const String _tableId = 'Ordenes';
  static const String _apiKey =
      'pat6OYXfRzbBUxrhR.88618729714dff569ff1782db8e2d7372556f194f544e2d801673e19a721f587';
  static const String _baseUrl = 'https://api.airtable.com/v0';

  Future<void> addOrder(List<Product> products, String paymentMethod) async {
    final fields = products
        .map((product) => {
              'fields': {
                'Producto': product.name,
                'Precio': product.price,
                'Método de Pago':
                    paymentMethod == 'cash' ? 'Efectivo' : 'Tarjeta',
                'Cantidad': product.quantity,
                'Monto total': product.price * product.quantity,
              }
            })
        .toList();
    final orderData = {'records': fields};

    final response = await http.post(
      Uri.parse('$_baseUrl/$_baseId/$_tableId'),
      headers: {
        'Authorization': 'Bearer $_apiKey',
        'Content-Type': 'application/json',
      },
      body: json.encode(orderData),
    );

    if (response.statusCode != 200) {
      throw Exception('Failed to add order to Airtable: ${response.body}');
    }
  }

  Future<List<Map<String, dynamic>>> getTodayOrders() async {
    final response = await http.get(
      Uri.parse('$_baseUrl/$_baseId/$_tableId?view=Hoy'),
      headers: {
        'Authorization': 'Bearer $_apiKey',
        'Content-Type': 'application/json',
      },
    );
    if (response.statusCode == 200) {
      final Map<String, dynamic> data = json.decode(response.body);
      final List<dynamic> records = data['records'];
      final List<Map<String, dynamic>> orders = [];
      for (final record in records) {
        final Map<String, dynamic> fields = record['fields'];
        orders.add(fields);
      }
      return orders;
    } else {
      throw Exception('Failed to load orders from Airtable');
    }
  }
}
